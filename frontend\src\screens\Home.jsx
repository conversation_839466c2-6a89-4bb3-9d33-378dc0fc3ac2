import React, { useState } from 'react';
import 'remixicon/fonts/remixicon.css';
import axios from '../config/axios';

export default function Dashboard() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [projectName, setProjectName] = useState('');

  const createProject = (e) => {
    e.preventDefault();
    console.log('Project Name:', projectName);
    axios.post('/project/create', { name: projectName })
      .then((response) => {
        console.log('Project created:', response.data);
         
      })
      .catch((error) => {
        console.error('Error creating project:', error);
      });
   setIsModalOpen(false);
    setProjectName('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 flex items-start p-10">
      <button
        onClick={() => setIsModalOpen(true)}
        className="flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-xl shadow-lg hover:bg-blue-700 transition-all duration-200"
      >
        <i className="ri-folder-add-line text-xl"></i>
        <span className="font-medium">New Project</span>
      </button>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex justify-center items-center z-50">
          <div className="bg-white rounded-2xl shadow-xl w-full max-w-md p-6 relative animate-fade-in">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                <i className="ri-folder-add-line mr-2 text-blue-600"></i>
                Create New Project
              </h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 text-xl"
              >
                <i className="ri-close-line"></i>
              </button>
            </div>

            <form onSubmit={createProject}>
              <label className="block mb-1 text-sm text-gray-600 font-medium">
                Project Name
              </label>
              <input
                type="text"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="e.g. AI Chat App"
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 outline-none mb-5"
              />

              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300 transition"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                >
                  Create
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
