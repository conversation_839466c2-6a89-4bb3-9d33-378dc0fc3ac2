import dotenv from 'dotenv';
dotenv.config();
import express from 'express';
import morgan from 'morgan';
import connect from './db/db.js';
import userRoutes from './routes/users.routes.js';
import cookieParser from 'cookie-parser';
import cors from 'cors'
import projectRoutes from './routes/project.routes.js';

connect();

const app = express();

app.use(cors());
app.use(morgan('dev'));

// Custom middleware to handle text/plain content type that contains JSON
app.use((req, res, next) => {
    if (req.headers['content-type'] === 'text/plain' && req.method === 'POST') {
        let data = '';
        req.on('data', chunk => {
            data += chunk;
        });
        req.on('end', () => {
            try {
                req.body = JSON.parse(data);
                console.log('Parsed text/plain as JSON:', req.body);
            } catch (e) {
                console.log('Failed to parse text/plain as JSON:', e.message);
                req.body = data;
            }
            next();
        });
    } else {
        next();
    }
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

app.get('/', (req, res) => {
    res.send('Hello From home');
});

app.use('/users',userRoutes);
app.use('/project',projectRoutes);

export default app;