import projectModel from '../models/project.model.js';
import * as projectService from '../services/project.services.js';
import { validationResult } from 'express-validator';
import userModel from '../models/user.models.js';

export const createProject = async (req, res) => {
    

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        console.log('Validation errors:', errors.array());
        return res.status(400).json({
            errors: errors.array()
        })
    }

    const { name } =  req.body;

    const loggedInUser = await userModel.findById(req.user._id);
    const users = [loggedInUser._id];

    try {
        const newProject = await projectService.createProject({
            name, users
        });

        res.status(201).json({
            newProject
        })
    }

    catch (error) {
        res.status(500).json({
            error: error.message
        })
    }
 
}

export const getAllProjects = async (req, res) => {
    try {
        const loggedInUser = await userModel.findOne(req.user.email);

        const projects = await projectModel.find({
            users: loggedInUser._id
        });

        res.status(200).json({
            projects
        });
    }
    catch (error) {
        res.status(500).json({
            error: error.message
        })
    }
}