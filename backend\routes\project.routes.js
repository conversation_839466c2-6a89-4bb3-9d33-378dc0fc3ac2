import {Router } from 'express';
import * as projectController from '../controllers/project.controller.js';
import { body } from 'express-validator';
import * as authMiddleware from '../middlewares/auth.middleware.js';

const router = Router();

router.post('/create',
    [body('name').isLength({ min: 3 }).withMessage('Name must be at least 3 characters')],
    authMiddleware.authUser,
    projectController.createProject);

export default router;