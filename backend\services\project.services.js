import projectModel from '../models/project.model.js';

export const createProject = async ({
    name, users
}) => {
    if (!name || !users) {
        throw new Error('Name and users are required');
    }

    try {
        const project = await projectModel.create({
            name,
            users
        });
        return project;
    } catch (error) {
        if (error.code === 11000) {
            throw new Error('Project with this name already exists');
        }
        throw error;
    }
}

export const getAllProjectByUserId = async (userId) => {
    if (!userId) {
        throw new Error('User ID is required');
    }

   const allUserProjects = await projectModel.find({
        users: userId
    });

    return allUserProjects;
}

